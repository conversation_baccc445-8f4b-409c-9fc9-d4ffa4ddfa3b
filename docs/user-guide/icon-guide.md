# 🌧️ 图标库使用指南

## 概述

FaciShare 导航现在支持 Font Awesome 图标库，为您的网站卡片提供更专业、更丰富的图标选择。

## 快速开始

### 基本用法

在网站配置中添加 `iconClass` 字段：

```json
{
  "id": "github",
  "name": "GitHub",
  "description": "全球最大的代码托管平台",
  "icon": "🐙",
  "iconClass": "fab fa-github",
  "url": "https://github.com/",
  "tags": ["代码", "开发", "git"]
}
```

### 优先级规则

1. **Font Awesome 优先**：如果提供了 `iconClass`，将优先显示 Font Awesome 图标
2. **Emoji 回退**：如果 `iconClass` 为空，则使用传统的 `icon` 字段（Emoji 或图片）
3. **默认图标**：如果都为空，显示默认图标 🌐

## 常用图标推荐

### 开发工具类
```json
"iconClass": "fas fa-code"          // 代码编辑器
"iconClass": "fab fa-github"        // GitHub
"iconClass": "fab fa-gitlab"        // GitLab
"iconClass": "fas fa-terminal"      // 终端/命令行
"iconClass": "fas fa-database"      // 数据库
"iconClass": "fas fa-server"        // 服务器
```

### 办公协作类
```json
"iconClass": "fas fa-envelope"      // 邮箱
"iconClass": "fas fa-calendar-alt"  // 日历/会议
"iconClass": "fas fa-tasks"         // 任务管理
"iconClass": "fas fa-users"         // 团队协作
"iconClass": "fas fa-file-alt"      // 文档
"iconClass": "fas fa-chart-bar"     // 数据分析
```

### 学习资源类
```json
"iconClass": "fas fa-book"          // 书籍/教程
"iconClass": "fas fa-graduation-cap" // 学习/教育
"iconClass": "fas fa-video"         // 视频教程
"iconClass": "fas fa-podcast"       // 播客
"iconClass": "fas fa-newspaper"     // 新闻/博客
```

### 工具应用类
```json
"iconClass": "fas fa-tools"         // 通用工具
"iconClass": "fas fa-wrench"        // 配置/设置
"iconClass": "fas fa-download"      // 下载工具
"iconClass": "fas fa-upload"        // 上传工具
"iconClass": "fas fa-compress"      // 压缩工具
"iconClass": "fas fa-shield-alt"    // 安全工具
```

## 图标类型说明

### Solid 图标 (fas)
最常用的实心图标，适合大多数场景：
```json
"iconClass": "fas fa-home"
"iconClass": "fas fa-user"
"iconClass": "fas fa-cog"
```

### Brand 图标 (fab)
品牌和社交媒体图标：
```json
"iconClass": "fab fa-github"
"iconClass": "fab fa-google"
"iconClass": "fab fa-microsoft"
```

### Regular 图标 (far)
轮廓样式图标，更轻量：
```json
"iconClass": "far fa-file"
"iconClass": "far fa-folder"
"iconClass": "far fa-heart"
```

## 最佳实践

### 1. 保持一致性
在同一分类下的网站使用相似风格的图标，保持视觉一致性。

### 2. 语义化选择
选择与网站功能相关的图标，提高用户理解度。

### 3. 向后兼容
保留原有的 `icon` 字段作为回退方案：
```json
{
  "icon": "📚",              // Emoji 回退
  "iconClass": "fas fa-book" // Font Awesome 优先
}
```

### 4. 测试验证
添加新图标后，在不同主题下测试显示效果。

## 故障排除

### 图标不显示
1. 检查 `iconClass` 拼写是否正确
2. 确认使用的是 Font Awesome 7.0.0 支持的图标
3. 查看浏览器控制台是否有错误信息

### 图标显示异常
1. 清除浏览器缓存
2. 检查网络连接
3. 确认 Font Awesome 样式文件加载正常

## 更多资源

- [Font Awesome 官方图标库](https://fontawesome.com/icons)
- [Font Awesome 6.7.0 文档](https://fontawesome.com/docs)
- [项目配置指南](./configuration.md)

---

**提示**：本功能完全向后兼容，现有的 Emoji 图标将继续正常工作。您可以逐步迁移到 Font Awesome 图标，或者混合使用两种方式。
