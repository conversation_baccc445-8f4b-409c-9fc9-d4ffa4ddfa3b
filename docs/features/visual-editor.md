# 🌧️ 导航页数据可视化编辑

这是一个基于Vue.js的纯前端导航页配置文件编辑工具，用于可视化编辑`firstshare.json`等导航配置文件。

### ✨ 功能特性

- **文件加载与可视化编辑**: 支持加载JSON配置文件，并在树状结构中进行增删改查。
- **数据校验**: 强制执行"分类下要么是网站，要么是子分类"的规则。
- **智能搜索**: 支持按名称、描述、标签、URL多字段搜索。
- **本地持久化**: 修改自动保存到浏览器本地存储，支持数据恢复。
- **文件导出**: 导出格式化的JSON文件，并以时间戳命名。
- **用户友好**: 响应式设计、键盘快捷键、优雅的消息提示和内置帮助。

### 🚀 快速开始

1. **打开工具**: 在浏览器中直接打开 `npve.html` 文件。
2. **加载数据**: 点击"加载JSON文件"并选择配置文件。
3. **编辑数据**: 在左侧树状列表选择项目，在右侧表单中修改。
4. **添加项目**: 点击"添加根分类"、"添加子分类"或"添加网站"。
5. **导出文件**: 点击"导出JSON"按钮生成带时间戳的文件。

### ⌨️ 快捷键

| 快捷键 | 功能 |
|--------|------|
| `Ctrl/Cmd + S` | 保存当前修改 |
| `Ctrl/Cmd + E` | 导出JSON文件 |
| `Delete` | 删除选中项 |
| `Escape` | 取消选择 |

### 🛠️ 技术栈

- **Vue.js 3**: 通过CDN引入，无需构建。
- **原生CSS**: 响应式设计。
- **localStorage & FileReader & Blob API**: 实现本地数据持久化和文件操作。

### 📝 总结

该后台管理工具健壮、用户友好且数据安全，能够有效提升导航页配置文件的编辑效率和准确性。