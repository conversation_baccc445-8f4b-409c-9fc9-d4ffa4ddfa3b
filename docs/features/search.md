# 🔍 强大的多功能搜索

导航页提供了一个强大且智能的搜索框，助您秒速定位所需站点。

- **模糊与容错搜索**: 基于强大的 `Fuse.js` 引擎，即使您输入了错别字或不完整的词语，系统也能大概率找到您想要的结果。
- **中英文首字母搜索**: 支持输入拼音首字母来快速查找中文站点。例如，输入 `bj` 可以快速定位到"北京..."。
- **管道筛选**: 您可以使用 `|` 符号进行分类预筛选。这极大地缩小了搜索范围，让结果更精确。
  - **语法**: `分类名 | 关键词`
  - **示例**: `常用工具 | git` 将只在"常用工具"分类下搜索包含"git"的站点。
- **权重排名**: 搜索结果经过精心设计的权重系统排序，优先展示名称和分类匹配度高的项目。
- **精准高亮**: 搜索关键词在结果的名称、描述、标签中都会被精确高亮显示。
- **快捷键支持**: `⌘K`/`Ctrl+K` 快速唤起搜索。
- **键盘导航**: 使用方向键 `↑/↓` 选择结果，`Enter` 键打开链接。
- **防抖优化**: 300ms防抖，避免在快速输入时发送不必要的搜索请求，提升性能。
- **状态管理**: 智能的搜索状态切换和重置逻辑。
