# 🔗 多链接卡片

为了提供更丰富的内容展示方式，每个网站卡片都支持网址链接和内联Markdown文档的灵活组合。系统会根据您在数据文件中的配置，自动渲染出不同类型的卡片。

### 卡片类型

#### 情况一：仅外部链接

当一个网站只配置了 `url` 字段时，它会渲染成一个标准的外部链接卡片。

**JSON配置示例:**
```json
{
  "id": "example-site",
  "name": "示例网站",
  "description": "这是一个外部网站",
  "icon": "🌐",
  "url": "https://example.com",
  "tags": ["工具", "外部"]
}
```
- **交互**: 点击整个卡片将直接在新标签页中打开 `https://example.com`。
- **视觉**: 卡片左侧有**绿色**边框指示器，右上角有绿色圆点，明确标识为外部链接。

#### 情况二：仅内联文档

当一个网站只配置了 `markdownFile` 字段时，它会渲染成一个内联文档卡片。

**JSON配置示例:**
```json
{
  "id": "local-doc",
  "name": "本地文档",
  "description": "这是一个本地Markdown文档",
  "icon": "📄",
  "markdownFile": "nav/data/docs/guide.md",
  "tags": ["文档", "指南"]
}
```
- **交互**: 点击整个卡片会以模态框的形式，在当前页面预览 `guide.md` 的内容。
- **视觉**: 卡片左侧有**橙色**边框指示器，右上角有橙色圆点，并带有一个"📚 文档"徽章，清晰标识为文档卡片。

#### 情况三：外部链接 + 内联文档 (双链接)

当一个网站同时配置了 `url` 和 `markdownFile` 字段时，它会渲染成一个功能强大的双链接卡片。

**JSON配置示例:**
```json
{
  "id": "dual-link-site",
  "name": "双链接网站",
  "description": "同时支持外部访问和本地文档",
  "icon": "🔗",
  "url": "https://example.com",
  "markdownFile": "nav/data/docs/example-guide.md",
  "tags": ["工具", "文档"]
}
```
- **交互**:
    - 点击卡片**主体区域**，会打开外部链接 `https://example.com`。
    - 点击右上角的**"文档"指示器**，则会预览内联文档 `example-guide.md`。
- **视觉**: 卡片左侧有**蓝色**边框指示器，右上角有蓝色圆点，并带有一个可独立点击的"文档"按钮，实现了两种功能的完美融合。

### 交互设计特点

- 🎯 **智能识别**: 系统自动根据 `url` 和 `markdownFile` 字段的有无来判断卡片类型。
- 🎨 **视觉区分**: 不同类型的卡片使用不同颜色的边框和指示器，一目了然。
- 👆 **精确点击**: 双链接卡片的文档指示器拥有独立的点击区域，不影响主卡片的链接跳转功能。
- 📱 **移动端适配**: 在紧凑的移动视图下，"文档"指示器会自动收缩为圆形图标，节省空间。
- ⚡ **性能优化**: 通过事件冒泡的智能处理，避免了复杂的点击冲突判断，保证了流畅的交互体验。

### 使用场景

**开发工具类**:
- **主链接**: 指向工具的官方网站。
- **文档链接**: 指向内部整理的安装指南、配置教程或最佳实践。

**学习资源类**:
- **主链接**: 指向在线教程网站。
- **文档链接**: 指向个人的学习笔记和重点总结。

**企业内部工具**:
- **主链接**: 指向线上生产系统。
- **文档链接**: 指向该系统的详细使用手册或常见问题解答。
