# FaciShare 导航

一个现代化的企业级导航页面，基于原生JavaScript构建，提供智能搜索、多主题切换、响应式设计和完整的状态管理功能。

![NavSphere](https://img.shields.io/badge/NavSphere-v2.0-blue) ![License](https://img.shields.io/badge/license-MIT-green) ![JavaScript](https://img.shields.io/badge/JavaScript-ES6+-yellow)

## 📖 文档目录

欢迎阅读项目文档！在这里您可以找到关于使用、配置和开发本导航项目的所有信息。

### 用户文档

- **[🚀 快速开始](./getting-started.md)**: 如何在本地运行和部署项目。
- **[⚙️ 数据配置指南](./configuration.md)**: 学习如何配置分类和网站数据。
- **[🎨 主题系统指南](./user-guide/theme-system.md)**: 了解所有可用主题以及如何配置它们。
- **[💎 图标使用指南](./user-guide/icon-guide.md)**: 如何为您的网站链接配置精美的图标。

### 功能详解

- **[🌟 核心特性](./features.md)**: 概览所有主要功能。
- **[🔍 搜索系统](./features/search.md)**: 深入了解强大的搜索功能。
- **[🔗 多链接卡片](./features/multi-link-cards.md)**: 学习如何使用集成了外部链接和内部文档的卡片。
- **[⏰ 时间范围提示](./features/time-notifications.md)**: 配置在特定时间段显示的友好提示。
- **[✏️ 可视化编辑器](./features/visual-editor.md)**: 使用图形化界面编辑您的导航数据。

### 开发者文档

- **[🛠️ 技术架构](./developers/architecture.md)**: 了解项目的核心架构、模块设计和开发实践。
- **[📚 API 文档](./developers/api.md)**: 查看可用的 JavaScript API。
- **[🤝 贡献指南](./developers/contributing.md)**: 如何为本项目贡献代码或提出建议。

---

### 📁 项目结构概览

```
fs-oss-navigation/
├── index.html             # 主页面入口
|── npve.html              # 导航页数据可视化编辑页面
├── nav/css/               # 样式系统
├── nav/js/                # JavaScript模块
├── nav/data/              # 数据层
├── nav/assets/            # 静态资源
└── docs/                  # 项目文档 (您现在所在的位置)
```
