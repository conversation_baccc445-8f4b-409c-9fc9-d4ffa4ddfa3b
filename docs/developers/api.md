# 📚 API文档

### 🌐 全局接口

#### NavApp 主应用接口

```javascript
// 获取应用实例
const app = NavApp.getInstance()

// 刷新应用数据
NavApp.refresh()

// 获取应用统计信息
const stats = NavApp.getStats()

// 切换主题
NavApp.switchTheme()
```

#### 主题管理接口

```javascript
// 设置主题
NavApp.theme.setTheme('jasmine-green')

// 获取当前主题信息
const themeInfo = NavApp.theme.getInfo()
// 返回: { theme: "jasmine-green", source: "manual", description: "手动设置" }

// 重置主题配置
NavApp.theme.reset()

// 配置时间主题
NavApp.theme.configTime({
    enabled: true,
    lightStart: 7,
    lightEnd: 19
})
```

#### 搜索管理接口

```javascript
// 执行搜索
navApp.searchManager.performSearch('关键词')

// 获取搜索统计
const searchStats = NavApp.search.getStats()

// 重置标签筛选器提示
NavApp.search.resetTagHints()
```

#### 🔗 多链接卡片API

```javascript
// 检查卡片链接类型
function checkCardLinkType(siteData) {
    const hasUrl = siteData.url && siteData.url.trim() !== '';
    const hasMarkdown = siteData.markdownFile && siteData.markdownFile.trim() !== '';
    
    if (hasUrl && hasMarkdown) {
        return 'both';        // 双链接卡片
    } else if (hasMarkdown) {
        return 'markdown';    // 仅文档卡片
    } else if (hasUrl) {
        return 'external';    // 仅网址卡片
    } else {
        return 'none';        // 无链接卡片
    }
}

// 手动触发卡片点击事件
function triggerCardClick(cardElement, clickType = 'main') {
    const event = new MouseEvent('click', { bubbles: true });
    
    if (clickType === 'document') {
        // 模拟点击文档指示器
        const docIndicator = cardElement.querySelector('.doc-indicator');
        if (docIndicator) {
            docIndicator.dispatchEvent(event);
        }
    } else {
        // 模拟点击主卡片
        cardElement.dispatchEvent(event);
    }
}

// 获取卡片链接信息
function getCardLinkInfo(cardElement) {
    return {
        siteId: cardElement.dataset.siteId,
        siteName: cardElement.dataset.siteName,
        url: cardElement.dataset.url || null,
        markdownFile: cardElement.dataset.markdownFile || null,
        linkType: checkCardLinkType({
            url: cardElement.dataset.url,
            markdownFile: cardElement.dataset.markdownFile
        })
    };
}
```

### 🔧 快速配置API

#### 时间主题快速配置

```javascript
// 应用预设配置
applyTimeThemePreset("nightOwl")

// 自定义时间段
NavSphereQuickConfig.setTimeTheme(7, 19)

// 禁用时间主题
NavSphereQuickConfig.disableTimeTheme()

// 查看当前配置
NavSphereQuickConfig.showConfig()
```

#### 时间范围提示API

```javascript
// 配置文件管理
TimeNotificationConfig.showConfig()                    // 查看当前配置
await TimeNotificationConfig.reloadConfig()            // 重新加载配置文件
await TimeNotificationConfig.getAvailablePresets()     // 获取可用预设
await TimeNotificationConfig.loadPreset('student')     // 加载预设配置

// 运行时管理
TimeNotificationConfig.addNotification({...})          // 添加通知
TimeNotificationConfig.removeNotification('id')        // 移除通知
TimeNotificationConfig.setEnabled(true/false)          // 启用/禁用功能
TimeNotificationConfig.clearAllRecords()               // 清除显示记录

// 获取信息
TimeNotificationConfig.getAllNotifications()           // 获取所有通知
TimeNotificationConfig.getEnabledNotifications()       // 获取启用的通知
```

#### 调试和监控

```javascript
// 获取移动端调试信息
NavApp.debug()

// 查看应用状态
console.log(NavApp.getStats())

// 启用调试模式
window.NavSphereConfig.debug = true