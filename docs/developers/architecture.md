# 🛠️ 开发指南

### 📋 开发环境

#### 环境要求

- **Node.js** 16+ (可选，用于开发工具)
- **现代浏览器** Chrome 80+, Firefox 75+, Safari 13+
- **代码编辑器** VS Code (推荐) 或其他现代编辑器
- **本地服务器** Python/Node.js/PHP 任一

#### 推荐工具

```json
{
  "vscode_extensions": [
    "ms-vscode.vscode-json",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.live-server"
  ]
}
```

### 🏗️ 架构设计

#### 模块化架构

```text
┌─────────────────────────────────────────┐
│                NavApp                   │  ← 主应用控制器
├─────────────────────────────────────────┤
│  ThemeManager  │  SearchManager         │  ← 功能管理器
│  SidebarManager│  MarkdownManager       │
│  VisitManager  │  Platform Utils        │
├─────────────────────────────────────────┤
│  DOM操作层      │  事件处理层              │  ← 底层服务
│  数据处理层      │  工具函数层              │
└─────────────────────────────────────────┘
```

#### 核心模块职责

| 模块 | 职责 | 主要功能 |
|------|------|----------|
| `NavApp` | 主应用控制器 | 生命周期管理、模块协调、数据流控制 |
| `ThemeManager` | 主题管理 | 多主题切换、时间自动切换、系统偏好 |
| `SearchManager` | 搜索引擎 | 实时搜索、权重排序、键盘导航 |
| `SidebarManager` | 侧边栏管理 | 分类导航、状态记忆、移动端适配 |
| `MarkdownManager` | 文档渲染 | Markdown解析、模态框展示 |
| `VisitManager` | 访问统计 | 访问记录、使用频率分析 |

#### 数据流设计

```text
数据源 (sites.json) → 数据加载 → 数据处理 → 状态管理
                                                ↓
用户交互 ← 界面渲染 ← 状态更新 ← 事件处理 ← 界面渲染
```

### 🔧 开发实践

#### 代码规范

```javascript
/**
 * 搜索管理器 - 负责处理全局搜索功能
 * Search Manager - Handles global search functionality
 */
class SearchManager {
    /**
     * 执行搜索操作
     * @param {string} query 搜索关键词
     * @returns {Array} 搜索结果
     */
    performSearch(query) {
        // 实现搜索逻辑
    }
}
```

#### 设计模式应用

- **单例模式** - 主应用实例和管理器实例
- **观察者模式** - 主题变化通知和状态更新
- **策略模式** - 主题切换策略和搜索匹配策略
- **工厂模式** - DOM元素创建和事件处理器
- **模块模式** - 功能封装和命名空间管理

## ⚡ 性能优化

### 🚀 加载性能

- **资源压缩** - HTML/CSS/JS 压缩
- **图片优化** - SVG图标，图片懒加载
- **Gzip压缩** - 服务器端启用Gzip
- **浏览器缓存** - 合理设置缓存策略

### 🏃 运行时性能

- **事件委托** - 减少事件监听器数量
- **防抖/节流** - 优化高频事件（搜索、滚动）
- **DOM操作优化** - 批量更新DOM，减少重绘
- **懒加载** - 图片和非首屏内容懒加载

### 📊 性能指标

| 指标 | 目标 | 优化策略 |
|------|------|----------|
| **FCP** | < 1s | 关键CSS内联，字体优化 |
| **LCP** | < 2.5s | 优化首屏内容加载 |
| **FID** | < 100ms | 减少主线程阻塞 |
| **CLS** | < 0.1 | 稳定布局，避免闪烁 |

## 🔍 故障排除

### 常见问题

- **Q: 为什么主题没有自动切换？**
  - A: 检查`js/config.js`中的时间主题配置是否正确，并确保没有手动设置过主题。

- **Q: 为什么搜索不到内容？**
  - A: 确认`data/sites.json`文件格式正确，并且搜索关键词在网站的名称、描述或标签中。

- **Q: 为什么Markdown文档无法显示？**
  - A: 检查`markdownFile`路径是否正确，并确保服务器可以访问该文件。

### 调试技巧

- **浏览器控制台** - 查看错误信息和调试输出
- **网络面板** - 检查资源加载情况
- **本地存储** - 查看`localStorage`中的状态数据
- **API调试** - 在控制台调用`NavApp`的API进行调试