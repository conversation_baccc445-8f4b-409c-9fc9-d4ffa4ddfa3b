# ⚙️ 数据配置指南

本指南详细说明了如何配置导航页的数据源、分类和网站信息。

## 1. 多数据源配置 (`appconfig.json`)

从 v2.0 开始，系统支持通过 `nav/data/appconfig.json` 配置文件来管理一个或多个网站数据源。这使得模块化管理不同类型或来源的网站列表成为可能。

**示例 `appconfig.json`:**
```json
{
  "version": "2.0",
  "description": "FaciShare 导航数据配置",
  "dataSources": [
    {
      "id": "foneshare-faci",
      "name": "Faci 网站数据",
      "path": "data/foneshare-faci.json",
      "enabled": true,
      "priority": 1
    },
    {
      "id": "work-tools",
      "name": "工作工具数据",
      "path": "data/work-tools.json",
      "enabled": true,
      "priority": 2
    }
  ]
}
```

### `dataSources` 字段说明

- `id` (必需): 数据源的唯一标识符。
- `name` (必需): 数据源的显示名称。
- `path` (必需): 数据源JSON文件的路径 (相对于 `nav/` 目录)。
- `enabled` (必需): `true` 或 `false`，决定是否加载此数据源。
- `priority` (可选): 加载优先级，数字越小优先级越高。
- `description` (可选): 数据源的描述信息。
- `domains` (可选): **域名绑定数组**。用于实现多租户或多环境部署。
    - **工作机制**:
      - 当此字段**存在且不为空**时（如 `["tenant-a.com", "www.tenant-a.com"]`），只有当导航页的当前域名完全匹配数组中的某个域名时，此数据源才会被加载。
      - 当此字段**不存在、为`null`或为空数组`[]`**时，此数据源被视为**默认数据源**，只要 `enabled` 为 `true`，它就会在所有域名下加载。
    - **用例**:
      - **多租户部署**: 为不同客户的域名配置不同的数据源，实现定制化内容。
      - **环境隔离**: 为开发环境（如 `localhost`, `127.0.0.1`）和生产环境配置不同的数据源。

## 2. 网站数据文件 (`*.json`)

每个由 `appconfig.json` 引用的数据文件都包含了分类和网站的层级结构。

### 整体结构

```json
{
  "categories": [
    {
      "id": "development",
      "name": "开发工具",
      "icon": "💻",
      "description": "开发相关的工具和资源",
      "sites": [
        {
          "id": "vscode",
          "name": "Visual Studio Code",
          "description": "微软开发的免费代码编辑器",
          "icon": "📝",
          "url": "https://code.visualstudio.com",
          "tags": ["编辑器", "开发", "微软", "免费"],
          "featured": true
        }
      ]
    }
  ]
}
```

### 分类 (Category) 字段

- `id` (必需): 唯一标识符，用于URL和内部引用。
- `name` (必需): 显示名称。
- `icon` (可选): 分类图标。详情请参阅 [图标使用指南](./user-guide/icon-guide.md)。
- `description` (可选): 分类的详细描述。
- `sites` (可选): 直接隶属于此分类的网站列表。
- `children` (可选): 子分类列表，最多支持3级嵌套。
- `order` (可选): 排序权重，数字越小越靠前。

### 网站 (Site) 字段

- `id` (必需): 网站的唯一标识符。
- `name` (必需): 网站显示的名称。
- `url` (可选): 网站的外部链接。
- `markdownFile` (可选): 内联Markdown文档的路径 (相对于 `nav/` 目录)。
- `description` (可选): 网站的详细描述。
- `icon` (可选): 网站图标。详情请参阅 [图标使用指南](./user-guide/icon-guide.md)。
- `iconClass` (可选): Font Awesome 图标类名。优先级高于 `icon`。
- `tags` (可选): 标签数组，用于搜索和筛选。
- `featured` (可选): `true` 或 `false`，是否为推荐网站。

**关于 `url` 和 `markdownFile` 的组合使用，请参阅 [多链接卡片功能详解](./features/multi-link-cards.md)。**
